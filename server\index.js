const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const session = require('express-session');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');
const { exec } = require('child_process');
require('dotenv').config();

const db = require('./config/database');
const authRoutes = require('./routes/auth');
const contentRoutes = require('./routes/content');
const adminRoutes = require('./routes/admin');
const uploadRoutes = require('./routes/upload');
const categoryRoutes = require('./routes/categories');
// trackingRoutes removed

const app = express();
const PORT = process.env.PORT || 3001;

// Trust proxy headers from Nginx
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: false, // Disable helmet CSP to avoid conflicts
  crossOriginEmbedderPolicy: false
}));

// Custom CSP middleware to allow Google Fonts - applied early to ensure it's not overridden
app.use((req, res, next) => {
  // Set comprehensive CSP with Google Fonts support
  res.setHeader('Content-Security-Policy',
    "default-src 'self'; " +
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
    "style-src-elem 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
    "font-src 'self' data: https://fonts.gstatic.com; " +
    "img-src 'self' data: https:; " +
    "connect-src 'self' https:; " +
    "media-src 'self' https:; " +
    "object-src 'none'; " +
    "frame-src 'self' https:; " +
    "base-uri 'self'; " +
    "form-action 'self'; " +
    "frame-ancestors 'self'"
  );
  next();
});



// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // limit each IP to 1000 requests per windowMs (increased for development)
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 500, // Increased limit for admin panel usage (was causing 429 errors)
  message: 'Too many authentication attempts, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/api/auth', authLimiter);
app.use('/api', limiter);

// CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:5173',
      'https://streamdb.online', // Your actual domain
      'https://www.streamdb.online', // With www
      'http://streamdb.online', // HTTP version for development
      'http://www.streamdb.online', // HTTP www version
      process.env.FRONTEND_URL
    ].filter(Boolean);

    // Log CORS requests for debugging
    console.log(`CORS request from origin: ${origin}`);

    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.warn(`CORS blocked origin: ${origin}`);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

app.use(cors(corsOptions));

// Body parsing middleware
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging
app.use(morgan('combined'));

// Session configuration - Using memory store permanently
// Memory store is sufficient for this application as sessions are primarily used for
// tracking and security logging, not critical user state persistence
console.log('✓ Using memory session store (sessions reset on server restart)');
const sessionStore = undefined; // Use default memory store

app.use(session({
  key: 'streamdb_session',
  secret: process.env.SESSION_SECRET || 'your-secret-key-change-this',
  store: sessionStore,
  resave: false,
  saveUninitialized: false,
  cookie: {
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production', // HTTPS only in production
    sameSite: 'lax'
  }
}));

// Static file serving for React build
const distPath = path.join(__dirname, '..', 'dist');
console.log('Serving static files from:', distPath);

// Configure static file serving with proper MIME types and security headers
app.use(express.static(distPath, {
  setHeaders: (res, filePath) => {
    // Set proper MIME types for different file extensions
    if (filePath.endsWith('.js') || filePath.endsWith('.mjs')) {
      res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
      res.setHeader('X-Content-Type-Options', 'nosniff');
    } else if (filePath.endsWith('.css')) {
      res.setHeader('Content-Type', 'text/css; charset=utf-8');
    } else if (filePath.endsWith('.html')) {
      res.setHeader('Content-Type', 'text/html; charset=utf-8');
    } else if (filePath.endsWith('.json')) {
      res.setHeader('Content-Type', 'application/json; charset=utf-8');
    } else if (filePath.endsWith('.png')) {
      res.setHeader('Content-Type', 'image/png');
    } else if (filePath.endsWith('.jpg') || filePath.endsWith('.jpeg')) {
      res.setHeader('Content-Type', 'image/jpeg');
    } else if (filePath.endsWith('.svg')) {
      res.setHeader('Content-Type', 'image/svg+xml');
    } else if (filePath.endsWith('.ico')) {
      res.setHeader('Content-Type', 'image/x-icon');
    } else if (filePath.endsWith('.webmanifest')) {
      res.setHeader('Content-Type', 'application/manifest+json');
    } else if (filePath.endsWith('.tsx') || filePath.endsWith('.ts')) {
      // Prevent serving TypeScript files directly
      res.status(404).end();
      return;
    }

    // Add cache headers for static assets
    if (filePath.includes('/assets/')) {
      res.setHeader('Cache-Control', 'public, max-age=31536000'); // 1 year
    } else if (filePath.endsWith('.html')) {
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    } else {
      res.setHeader('Cache-Control', 'public, max-age=3600'); // 1 hour
    }
  }
}));

// Favicon and icon routes with proper MIME types
app.get('/favicon.ico', (req, res) => {
  res.setHeader('Content-Type', 'image/x-icon');
  res.setHeader('Cache-Control', 'public, max-age=86400'); // 24 hours
  res.sendFile(path.join(distPath, 'favicon.ico'));
});

app.get('/favicon-16x16.png', (req, res) => {
  res.setHeader('Content-Type', 'image/png');
  res.setHeader('Cache-Control', 'public, max-age=86400');
  res.sendFile(path.join(distPath, 'favicon-16x16.png'));
});

app.get('/favicon-32x32.png', (req, res) => {
  res.setHeader('Content-Type', 'image/png');
  res.setHeader('Cache-Control', 'public, max-age=86400');
  res.sendFile(path.join(distPath, 'favicon-32x32.png'));
});

app.get('/apple-touch-icon.png', (req, res) => {
  res.setHeader('Content-Type', 'image/png');
  res.setHeader('Cache-Control', 'public, max-age=86400');
  res.sendFile(path.join(distPath, 'apple-touch-icon.png'));
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    distPath: distPath,
    distExists: fs.existsSync(distPath)
  });
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/content', contentRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/upload', uploadRoutes);
app.use('/api/categories', categoryRoutes);
app.use('/api/sections', require('./routes/sections'));
// tracking routes removed

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  
  // Handle specific error types
  if (err.name === 'ValidationError') {
    return res.status(400).json({
      error: 'Validation Error',
      message: err.message,
      details: err.details || []
    });
  }
  
  if (err.name === 'UnauthorizedError') {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Invalid or expired token'
    });
  }
  
  if (err.code === 'LIMIT_FILE_SIZE') {
    return res.status(413).json({
      error: 'File Too Large',
      message: 'File size exceeds the maximum allowed limit'
    });
  }
  
  // Default error response
  res.status(500).json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'production' 
      ? 'Something went wrong' 
      : err.message
  });
});

// Catch-all handler: send back React's index.html file for client-side routing
app.get('*', (req, res) => {
  // Only serve index.html for non-API routes and non-static assets
  const isApiRoute = req.path.startsWith('/api/');
  const isStaticAsset = req.path.startsWith('/assets/') || 
                       req.path.includes('.js') || 
                       req.path.includes('.css') || 
                       req.path.includes('.png') || 
                       req.path.includes('.jpg') || 
                       req.path.includes('.jpeg') || 
                       req.path.includes('.svg') || 
                       req.path.includes('.ico') || 
                       req.path.includes('.webmanifest') || 
                       req.path.includes('.txt');
  
  if (isApiRoute) {
    // API routes that don't exist should return 404 JSON
    res.status(404).json({
      error: 'Not Found',
      message: 'The requested API resource was not found'
    });
  } else if (isStaticAsset) {
    // Static assets that don't exist should return 404
    res.status(404).send('File not found');
  } else {
    // Serve index.html for client-side routing
    const indexPath = path.join(distPath, 'index.html');
    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    res.sendFile(indexPath, (err) => {
      if (err) {
        console.error('Error serving index.html:', err);
        res.status(500).send('Internal Server Error');
      }
    });
  }
});

// Database connection test
async function testDatabaseConnection() {
  try {
    await db.execute('SELECT 1');
    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.warn('⚠️ Continuing without database for static file testing...');
    return false;
  }
}

// Start server
async function startServer() {
  try {
    const dbConnected = await testDatabaseConnection();

    app.listen(PORT, () => {
      console.log(`🚀 Server running on port ${PORT}`);
      console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
      console.log(`🌐 Static files: http://localhost:${PORT}`);
      if (dbConnected) {
        console.log(`💾 Database: ${process.env.DB_NAME}@${process.env.DB_HOST}`);
      } else {
        console.log(`⚠️ Database: Not connected (static file mode)`);
      }
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

startServer();
