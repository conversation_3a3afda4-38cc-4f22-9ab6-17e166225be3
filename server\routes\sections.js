/**
 * Dynamic Content Sections API Routes
 * Handles CRUD operations for dynamic content sections
 */
const express = require('express');
const router = express.Router();
const { v4: uuidv4 } = require('uuid');
const { body, validationResult } = require('express-validator');
const db = require('../config/database');
const { authenticateToken, requireModerator } = require('../middleware/auth');
/**
 * Get all content sections
 * GET /api/sections
 */
router.get('/', async (req, res) => {
  try {
    const { active_only = 'false', include_categories = 'false' } = req.query;
    let query = `
      SELECT 
        s.*,
        COUNT(c.id) as content_count
      FROM content_sections s
      LEFT JOIN content c ON s.id = c.section_id AND c.is_published = TRUE
    `;
    const params = [];
    if (active_only === 'true') {
      query += ' WHERE s.is_active = TRUE';
    }
    query += `
      GROUP BY s.id
      ORDER BY s.display_order ASC, s.name ASC
    `;
    const sections = await db.execute(query, params);
    // Parse JSON fields
    const processedSections = sections.map(section => ({
      ...section,
      content_types: typeof section.content_types === "string" ? JSON.parse(section.content_types) : (section.content_types || []),
      filter_rules: typeof section.filter_rules === "string" ? JSON.parse(section.filter_rules) : (section.filter_rules || {}),
    }));
    // Include categories if requested
    if (include_categories === 'true') {
      for (const section of processedSections) {
        const categories = await db.execute(`
          SELECT c.*, sc.is_default
          FROM categories c
          JOIN section_categories sc ON c.id = sc.category_id
          WHERE sc.section_id = ?
          ORDER BY sc.is_default DESC, c.name ASC
        `, [section.id]);
        section.categories = categories;
      }
    }
    res.json({
      success: true,
      data: processedSections,
      total: processedSections.length
    });
  } catch (error) {
    console.error('Failed to fetch sections:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch content sections'
    });
  }
});
/**
 * Get single section by ID or slug
 * GET /api/sections/:identifier
 */
router.get('/:identifier', async (req, res) => {
  try {
    const { identifier } = req.params;
    const isNumeric = /^\d+$/.test(identifier);
    const query = `
      SELECT 
        s.*,
        COUNT(c.id) as content_count
      FROM content_sections s
      LEFT JOIN content c ON s.id = c.section_id AND c.is_published = TRUE
      WHERE ${isNumeric ? 's.id = ?' : 's.slug = ?'}
      GROUP BY s.id
    `;
    const sections = await db.execute(query, [identifier]);
    if (sections.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Content section not found'
      });
    }
    const section = sections[0];
    // Parse JSON fields
    section.content_types = typeof section.content_types === 'string' ? JSON.parse(section.content_types) : (section.content_types || []);
    section.filter_rules = typeof section.filter_rules === 'string' ? JSON.parse(section.filter_rules) : (section.filter_rules || {});
    // Get associated categories
    const categories = await db.execute(`
      SELECT c.*, sc.is_default
      FROM categories c
      JOIN section_categories sc ON c.id = sc.category_id
      WHERE sc.section_id = ?
      ORDER BY sc.is_default DESC, c.name ASC
    `, [section.id]);
    section.categories = categories;
    res.json({
      success: true,
      data: section
    });
  } catch (error) {
    console.error('Failed to fetch section:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch content section'
    });
  }
});
/**
 * Create new content section
 * POST /api/sections
 */
router.post('/', 
  authenticateToken, 
  requireModerator,
  [
    body('name').trim().isLength({ min: 1, max: 100 }).withMessage('Name is required and must be 1-100 characters'),
    body('slug').trim().isLength({ min: 1, max: 100 }).withMessage('Slug is required and must be 1-100 characters'),
    body('description').optional().isLength({ max: 1000 }).withMessage('Description must be less than 1000 characters'),
    body('icon').optional().isLength({ max: 50 }).withMessage('Icon must be less than 50 characters'),
    body('color').optional().isLength({ max: 20 }).withMessage('Color must be less than 20 characters'),
    body('display_order').optional().isInt({ min: 0 }).withMessage('Display order must be a non-negative integer'),
    body('content_types').optional().isArray().withMessage('Content types must be an array'),
    body('filter_rules').optional().isObject().withMessage('Filter rules must be an object'),
    body('category_ids').optional().isArray().withMessage('Category IDs must be an array'),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation Error',
          details: errors.array()
        });
      }
      const {
        name, slug, description = '', icon = '', color = '', 
        display_order = 0, is_active = true, show_in_navigation = true,
        show_on_homepage = true, max_items_homepage = 20,
        content_types = [], filter_rules = {}, category_ids = []
      } = req.body;
      // Check if slug already exists
      const existingSections = await db.execute(
        'SELECT id FROM content_sections WHERE slug = ?',
        [slug]
      );
      if (existingSections.length > 0) {
        return res.status(400).json({
          error: 'Validation Error',
          message: 'A section with this slug already exists'
        });
      }
      // Create the section
      const result = await db.execute(`
        INSERT INTO content_sections (
          name, slug, description, icon, color, display_order,
          is_active, show_in_navigation, show_on_homepage, max_items_homepage,
          content_types, filter_rules
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        name, slug, description, icon, color, display_order,
        is_active, show_in_navigation, show_on_homepage, max_items_homepage,
        JSON.stringify(content_types), JSON.stringify(filter_rules)
      ]);
      const sectionId = result.insertId;
      // Link categories if provided
      if (category_ids.length > 0) {
        const categoryValues = category_ids.map(categoryId => [sectionId, categoryId, false]);
        await db.execute(`
          INSERT INTO section_categories (section_id, category_id, is_default)
          VALUES ${categoryValues.map(() => '(?, ?, ?)').join(', ')}
        `, categoryValues.flat());
      }
      res.status(201).json({
        success: true,
        id: sectionId,
        message: 'Content section created successfully'
      });
    } catch (error) {
      console.error('Failed to create section:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to create content section'
      });
    }
  }
);
/**
 * Update content section
 * PUT /api/sections/:id
 */
router.put('/:id',
  authenticateToken,
  requireModerator,
  [
    body('name').optional().trim().isLength({ min: 1, max: 100 }).withMessage('Name must be 1-100 characters'),
    body('slug').optional().trim().isLength({ min: 1, max: 100 }).withMessage('Slug must be 1-100 characters'),
    body('description').optional().isLength({ max: 1000 }).withMessage('Description must be less than 1000 characters'),
    body('icon').optional().isLength({ max: 50 }).withMessage('Icon must be less than 50 characters'),
    body('color').optional().isLength({ max: 20 }).withMessage('Color must be less than 20 characters'),
    body('display_order').optional().isInt({ min: 0 }).withMessage('Display order must be a non-negative integer'),
    body('content_types').optional().isArray().withMessage('Content types must be an array'),
    body('filter_rules').optional().isObject().withMessage('Filter rules must be an object'),
    body('category_ids').optional().isArray().withMessage('Category IDs must be an array'),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation Error',
          details: errors.array()
        });
      }
      const { id } = req.params;
      const updateData = req.body;
      // Check if section exists
      const existingSections = await db.execute(
        'SELECT id FROM content_sections WHERE id = ?',
        [id]
      );
      if (existingSections.length === 0) {
        return res.status(404).json({
          error: 'Not Found',
          message: 'Content section not found'
        });
      }
      // Check if slug is unique (if being updated)
      if (updateData.slug) {
        const slugCheck = await db.execute(
          'SELECT id FROM content_sections WHERE slug = ? AND id != ?',
          [updateData.slug, id]
        );
        if (slugCheck.length > 0) {
          return res.status(400).json({
            error: 'Validation Error',
            message: 'A section with this slug already exists'
          });
        }
      }
      // Build update query dynamically
      const updateFields = [];
      const updateValues = [];
      const allowedFields = [
        'name', 'slug', 'description', 'icon', 'color', 'display_order',
        'is_active', 'show_in_navigation', 'show_on_homepage', 'max_items_homepage'
      ];
      allowedFields.forEach(field => {
        if (updateData[field] !== undefined) {
          updateFields.push(`${field} = ?`);
          updateValues.push(updateData[field]);
        }
      });
      // Handle JSON fields
      if (updateData.content_types !== undefined) {
        updateFields.push('content_types = ?');
        updateValues.push(JSON.stringify(updateData.content_types));
      }
      if (updateData.filter_rules !== undefined) {
        updateFields.push('filter_rules = ?');
        updateValues.push(JSON.stringify(updateData.filter_rules));
      }
      if (updateFields.length > 0) {
        updateFields.push('updated_at = CURRENT_TIMESTAMP');
        updateValues.push(id);
        await db.execute(`
          UPDATE content_sections 
          SET ${updateFields.join(', ')}
          WHERE id = ?
        `, updateValues);
      }
      // Update category associations if provided
      if (updateData.category_ids !== undefined) {
        // Remove existing associations
        await db.execute('DELETE FROM section_categories WHERE section_id = ?', [id]);
        // Add new associations
        if (updateData.category_ids.length > 0) {
          const categoryValues = updateData.category_ids.map(categoryId => [id, categoryId, false]);
          await db.execute(`
            INSERT INTO section_categories (section_id, category_id, is_default)
            VALUES ${categoryValues.map(() => '(?, ?, ?)').join(', ')}
          `, categoryValues.flat());
        }
      }
      res.json({
        success: true,
        message: 'Content section updated successfully'
      });
    } catch (error) {
      console.error('Failed to update section:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to update content section'
      });
    }
  }
);
/**
 * Delete content section
 * DELETE /api/sections/:id
 */
router.delete('/:id',
  authenticateToken,
  requireModerator,
  async (req, res) => {
    try {
      const { id } = req.params;
      // Check if section exists
      const existingSections = await db.execute(
        'SELECT id, name FROM content_sections WHERE id = ?',
        [id]
      );
      if (existingSections.length === 0) {
        return res.status(404).json({
          error: 'Not Found',
          message: 'Content section not found'
        });
      }
      const section = existingSections[0];
      // Check if section has content
      const contentCount = await db.execute(
        'SELECT COUNT(*) as count FROM content WHERE section_id = ?',
        [id]
      );
      if (contentCount[0].count > 0) {
        return res.status(400).json({
          error: 'Validation Error',
          message: `Cannot delete section "${section.name}" because it contains ${contentCount[0].count} content items. Please move or delete the content first.`
        });
      }
      // Delete the section (cascading will handle section_categories)
      await db.execute('DELETE FROM content_sections WHERE id = ?', [id]);
      res.json({
        success: true,
        message: `Content section "${section.name}" deleted successfully`
      });
    } catch (error) {
      console.error('Failed to delete section:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to delete content section'
      });
    }
  }
);

/**
 * Reorder sections
 * PUT /api/sections/reorder
 */
router.put('/reorder', authenticateToken, requireModerator, async (req, res) => {
  try {
    const updateData = req.body;

    if (!Array.isArray(updateData) || updateData.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Update data must be a non-empty array'
      });
    }

    // Validate each update item
    for (const item of updateData) {
      if (!item.id || typeof item.display_order !== 'number') {
        return res.status(400).json({
          error: 'Validation Error',
          message: 'Each item must have id and display_order'
        });
      }
    }

    // Update each section's display order
    for (const item of updateData) {
      await db.execute(
        'UPDATE content_sections SET display_order = ? WHERE id = ?',
        [item.display_order, item.id]
      );
    }

    res.json({
      success: true,
      message: 'Sections reordered successfully'
    });

  } catch (error) {
    console.error('Error reordering sections:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to reorder sections'
    });
  }
});

/**
 * Get content for a specific section
 * GET /api/sections/:id/content
 */
router.get('/:id/content', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      page = 1,
      limit = 20,
      published_only = 'true',
      sort_by = 'created_at',
      sort_order = 'desc'
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);

    // Check if section exists
    const sections = await db.execute(
      'SELECT * FROM content_sections WHERE id = ?',
      [id]
    );

    if (sections.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Content section not found'
      });
    }

    const section = sections[0];

    // Build content query
    let contentQuery = `
      SELECT c.*, cat.name as category_name
      FROM content c
      LEFT JOIN categories cat ON c.category = cat.slug
      WHERE c.section_id = ?
    `;
    const queryParams = [id];

    if (published_only === 'true') {
      contentQuery += ' AND c.is_published = TRUE';
    }

    // Add sorting
    const allowedSortFields = ['created_at', 'updated_at', 'title', 'year', 'imdb_rating'];
    const sortField = allowedSortFields.includes(sort_by) ? sort_by : 'created_at';
    const sortDirection = sort_order.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
    contentQuery += ` ORDER BY c.${sortField} ${sortDirection}`;

    // Add pagination
    contentQuery += ' LIMIT ? OFFSET ?';
    const limitValue = parseInt(limit) || 20;
    const offsetValue = parseInt(offset) || 0;
    queryParams.push(limitValue, offsetValue);

    const content = await db.execute(contentQuery, queryParams);

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total
      FROM content c
      WHERE c.section_id = ?
    `;
    const countParams = [id];

    if (published_only === 'true') {
      countQuery += ' AND c.is_published = TRUE';
    }

    const countResult = await db.execute(countQuery, countParams);
    const total = countResult[0].total;

    // Process content data
    const processedContent = content.map(item => ({
      ...item,
      genres: item.genres ? JSON.parse(item.genres) : [],
      languages: item.languages ? JSON.parse(item.languages) : [],
      quality: item.quality ? JSON.parse(item.quality) : [],
      audio_tracks: item.audio_tracks ? JSON.parse(item.audio_tracks) : [],
    }));

    res.json({
      success: true,
      data: processedContent,
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / parseInt(limit)),
      section: {
        id: section.id,
        name: section.name,
        slug: section.slug
      }
    });

  } catch (error) {
    console.error('Failed to fetch section content:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to fetch section content'
    });
  }
});
/**
 * Update section display order
 * PUT /api/sections/reorder
 */
router.put('/reorder',
  authenticateToken,
  requireModerator,
  [
    body('sections').isArray().withMessage('Sections must be an array'),
    body('sections.*.id').isInt().withMessage('Section ID must be an integer'),
    body('sections.*.display_order').isInt({ min: 0 }).withMessage('Display order must be a non-negative integer'),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation Error',
          details: errors.array()
        });
      }
      const { sections } = req.body;
      // Update display order for each section
      for (const section of sections) {
        await db.execute(
          'UPDATE content_sections SET display_order = ? WHERE id = ?',
          [section.display_order, section.id]
        );
      }
      res.json({
        success: true,
        message: 'Section display order updated successfully'
      });
    } catch (error) {
      console.error('Failed to reorder sections:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to update section order'
      });
    }
  }
);
module.exports = router;
