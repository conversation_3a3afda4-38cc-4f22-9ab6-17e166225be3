const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const db = require('../config/database');
const { authenticateToken, requireModerator } = require('../middleware/auth');

// Validation middleware for content
const contentValidation = [
  body('title').trim().isLength({ min: 1, max: 255 }).withMessage('Title is required and must be 1-255 characters'),
  body('type').isIn(['movie', 'series', 'requested']).withMessage('Type must be movie, series, or requested'),
  body('description').optional().isLength({ max: 5000 }).withMessage('Description must be less than 5000 characters'),
  body('year').optional().isInt({ min: 1900, max: 2030 }).withMessage('Year must be between 1900 and 2030'),
  body('imdb_rating').optional().isFloat({ min: 0, max: 10 }).withMessage('IMDB rating must be between 0 and 10'),
  body('runtime').optional().isInt({ min: 1 }).withMessage('Runtime must be a positive integer'),
  body('is_published').optional().isBoolean().withMessage('is_published must be a boolean'),
  body('is_featured').optional().isBoolean().withMessage('is_featured must be a boolean')
];

// Get all content with filtering and pagination
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      type,
      category,
      published,
      featured,
      search,
      sort_by = 'created_at',
      sort_order = 'desc'
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    let query = `
      SELECT
        c.*,
        cat.name as category_name,
        cat.slug as category_slug,
        s.name as section_name,
        s.slug as section_slug
      FROM content c
      LEFT JOIN categories cat ON c.category = cat.slug
      LEFT JOIN content_sections s ON c.section_id = s.id
      WHERE 1=1
    `;
    
    const params = [];

    // Add filters
    if (type) {
      query += ' AND c.type = ?';
      params.push(type);
    }

    if (category) {
      query += ' AND c.category = ?';
      params.push(category);
    }

    if (published !== undefined) {
      query += ' AND c.is_published = ?';
      params.push(published === 'true' ? 1 : 0);
    }

    if (featured !== undefined) {
      query += ' AND c.is_featured = ?';
      params.push(featured === 'true' ? 1 : 0);
    }

    if (search) {
      query += ' AND (c.title LIKE ? OR c.description LIKE ? OR c.tags LIKE ?)';
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    // Add sorting
    const allowedSortFields = ['title', 'year', 'created_at', 'updated_at', 'imdb_rating'];
    const sortField = allowedSortFields.includes(sort_by) ? sort_by : 'created_at';
    const sortDirection = sort_order.toLowerCase() === 'asc' ? 'ASC' : 'DESC';
    
    query += ` ORDER BY c.${sortField} ${sortDirection}`;
    query += ` LIMIT ${parseInt(limit)} OFFSET ${offset}`;

    const content = await db.execute(query, params);

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total 
      FROM content c 
      LEFT JOIN categories cat ON c.category = cat.slug 
      WHERE 1=1
    `;
    
    const countParams = [];
    
    if (type) {
      countQuery += ' AND c.type = ?';
      countParams.push(type);
    }

    if (category) {
      countQuery += ' AND c.category = ?';
      countParams.push(category);
    }

    if (published !== undefined) {
      countQuery += ' AND c.is_published = ?';
      countParams.push(published === 'true' ? 1 : 0);
    }

    if (featured !== undefined) {
      countQuery += ' AND c.is_featured = ?';
      countParams.push(featured === 'true' ? 1 : 0);
    }

    if (search) {
      countQuery += ' AND (c.title LIKE ? OR c.description LIKE ? OR c.tags LIKE ?)';
      const searchTerm = `%${search}%`;
      countParams.push(searchTerm, searchTerm, searchTerm);
    }

    const [countResult] = await db.execute(countQuery, countParams);
    const total = countResult.total;

    res.json({
      success: true,
      data: content,
      total, // Include total at root level for backward compatibility
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / parseInt(limit)),
        hasNext: parseInt(page) < Math.ceil(total / parseInt(limit)),
        hasPrev: parseInt(page) > 1
      }
    });

  } catch (error) {
    console.error('Error fetching content:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to fetch content'
    });
  }
});

// Get single content item by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const query = `
      SELECT
        c.*,
        cat.name as category_name,
        cat.slug as category_slug,
        s.name as section_name,
        s.slug as section_slug
      FROM content c
      LEFT JOIN categories cat ON c.category = cat.slug
      LEFT JOIN content_sections s ON c.section_id = s.id
      WHERE c.id = ?
    `;
    
    const [content] = await db.execute(query, [id]);
    
    if (!content) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'Content not found'
      });
    }

    res.json({
      success: true,
      data: content
    });

  } catch (error) {
    console.error('Error fetching content:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to fetch content'
    });
  }
});

// Create new content (admin/moderator only)
router.post('/', authenticateToken, requireModerator, contentValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const {
      title,
      type,
      category,
      section,
      tmdbId,
      year,
      genres = [],
      languages = [],
      description,
      posterUrl,
      thumbnailUrl,
      videoLinks,
      secureVideoLinks,
      quality = [],
      tags,
      imdbRating,
      runtime,
      studio,
      audioTracks = [],
      trailer,
      subtitleUrl,
      isPublished = false,
      isFeatured = false,
      addToCarousel = false
    } = req.body;

    // Convert arrays to JSON strings for database storage
    const genresJson = JSON.stringify(Array.isArray(genres) ? genres : []);
    const languagesJson = JSON.stringify(Array.isArray(languages) ? languages : []);
    const qualityJson = JSON.stringify(Array.isArray(quality) ? quality : []);
    const audioTracksJson = JSON.stringify(Array.isArray(audioTracks) ? audioTracks : []);

    // Generate unique ID for content
    const contentId = `content_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Convert section slug to section_id
    let sectionId = null;
    if (section) {
      const [sectionResult] = await db.execute('SELECT id FROM content_sections WHERE slug = ? OR id = ?', [section, section]);
      if (sectionResult.length > 0) {
        sectionId = sectionResult[0].id;
      }
    }

    const query = `
      INSERT INTO content (
        id, title, type, category, section_id, tmdb_id, year, genres, languages,
        description, poster_url, thumbnail_url, secure_video_links,
        quality, tags, imdb_rating, runtime, studio, audio_tracks, trailer,
        subtitle_url, is_published, is_featured, add_to_carousel, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `;

    const result = await db.execute(query, [
      contentId,
      title,
      type,
      category,
      sectionId,
      tmdbId || null,
      year || null,
      genresJson,
      languagesJson,
      description || null,
      posterUrl || null,
      thumbnailUrl || null,
      secureVideoLinks || videoLinks || null,
      qualityJson,
      tags || null,
      imdbRating || null,
      runtime || null,
      studio || null,
      audioTracksJson,
      trailer || null,
      subtitleUrl || null,
      isPublished ? 1 : 0,
      isFeatured ? 1 : 0,
      addToCarousel ? 1 : 0
    ]);

    res.status(201).json({
      success: true,
      message: 'Content created successfully',
      data: {
        id: contentId,
        title,
        type,
        category
      }
    });

  } catch (error) {
    console.error('Error creating content:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to create content'
    });
  }
});

// Update content (admin/moderator only)
router.put('/:id', authenticateToken, requireModerator, contentValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const { id } = req.params;
    const {
      title,
      type,
      category,
      section,
      tmdbId,
      year,
      genres = [],
      languages = [],
      description,
      posterUrl,
      thumbnailUrl,
      videoLinks,
      secureVideoLinks,
      quality = [],
      tags,
      imdbRating,
      runtime,
      studio,
      audioTracks = [],
      trailer,
      subtitleUrl,
      isPublished = false,
      isFeatured = false,
      addToCarousel = false
    } = req.body;

    // Check if content exists
    const [existingContent] = await db.execute('SELECT id FROM content WHERE id = ?', [id]);
    if (!existingContent) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'Content not found'
      });
    }

    // Convert arrays to JSON strings for database storage
    const genresJson = JSON.stringify(Array.isArray(genres) ? genres : []);
    const languagesJson = JSON.stringify(Array.isArray(languages) ? languages : []);
    const qualityJson = JSON.stringify(Array.isArray(quality) ? quality : []);
    const audioTracksJson = JSON.stringify(Array.isArray(audioTracks) ? audioTracks : []);

    // Convert section slug to section_id
    let sectionId = null;
    if (section) {
      const [sectionResult] = await db.execute('SELECT id FROM content_sections WHERE slug = ? OR id = ?', [section, section]);
      if (sectionResult.length > 0) {
        sectionId = sectionResult[0].id;
      }
    }

    const query = `
      UPDATE content SET
        title = ?, type = ?, category = ?, section_id = ?, tmdb_id = ?, year = ?,
        genres = ?, languages = ?, description = ?, poster_url = ?, thumbnail_url = ?,
        secure_video_links = ?, quality = ?, tags = ?, imdb_rating = ?,
        runtime = ?, studio = ?, audio_tracks = ?, trailer = ?, subtitle_url = ?,
        is_published = ?, is_featured = ?, add_to_carousel = ?, updated_at = NOW()
      WHERE id = ?
    `;

    await db.execute(query, [
      title,
      type,
      category,
      sectionId,
      tmdbId || null,
      year || null,
      genresJson,
      languagesJson,
      description || null,
      posterUrl || null,
      thumbnailUrl || null,
      secureVideoLinks || videoLinks || null,
      qualityJson,
      tags || null,
      imdbRating || null,
      runtime || null,
      studio || null,
      audioTracksJson,
      trailer || null,
      subtitleUrl || null,
      isPublished ? 1 : 0,
      isFeatured ? 1 : 0,
      addToCarousel ? 1 : 0,
      id
    ]);

    res.json({
      success: true,
      message: 'Content updated successfully',
      data: {
        id,
        title,
        type,
        category
      }
    });

  } catch (error) {
    console.error('Error updating content:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to update content'
    });
  }
});

// Delete content (admin/moderator only)
router.delete('/:id', authenticateToken, requireModerator, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if content exists
    const [existingContent] = await db.execute('SELECT id, title FROM content WHERE id = ?', [id]);
    if (!existingContent) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'Content not found'
      });
    }

    await db.execute('DELETE FROM content WHERE id = ?', [id]);

    res.json({
      success: true,
      message: 'Content deleted successfully',
      data: {
        id,
        title: existingContent.title
      }
    });

  } catch (error) {
    console.error('Error deleting content:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to delete content'
    });
  }
});

// Bulk operations (admin/moderator only)
router.post('/bulk', authenticateToken, requireModerator, async (req, res) => {
  try {
    const { operation, contentIds, data = {} } = req.body;

    if (!operation || !Array.isArray(contentIds) || contentIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: 'Operation and contentIds array are required'
      });
    }

    const placeholders = contentIds.map(() => '?').join(',');
    let query;
    let params;

    switch (operation) {
      case 'delete':
        query = `DELETE FROM content WHERE id IN (${placeholders})`;
        params = contentIds;
        break;

      case 'publish':
        query = `UPDATE content SET is_published = 1, updated_at = NOW() WHERE id IN (${placeholders})`;
        params = contentIds;
        break;

      case 'unpublish':
        query = `UPDATE content SET is_published = 0, updated_at = NOW() WHERE id IN (${placeholders})`;
        params = contentIds;
        break;

      case 'feature':
        query = `UPDATE content SET is_featured = 1, updated_at = NOW() WHERE id IN (${placeholders})`;
        params = contentIds;
        break;

      case 'unfeature':
        query = `UPDATE content SET is_featured = 0, updated_at = NOW() WHERE id IN (${placeholders})`;
        params = contentIds;
        break;

      case 'update_category':
        if (!data.category) {
          return res.status(400).json({
            success: false,
            error: 'Validation Error',
            message: 'Category is required for update_category operation'
          });
        }
        query = `UPDATE content SET category = ?, updated_at = NOW() WHERE id IN (${placeholders})`;
        params = [data.category, ...contentIds];
        break;

      default:
        return res.status(400).json({
          success: false,
          error: 'Validation Error',
          message: 'Invalid operation'
        });
    }

    const result = await db.execute(query, params);

    res.json({
      success: true,
      message: `Bulk ${operation} completed successfully`,
      data: {
        operation,
        affectedRows: result.affectedRows,
        contentIds
      }
    });

  } catch (error) {
    console.error('Error in bulk operation:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to perform bulk operation'
    });
  }
});

module.exports = router;
