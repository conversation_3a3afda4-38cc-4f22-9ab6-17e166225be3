// Type definitions for sections and categories

export interface ContentSection {
  id: string;
  name: string;
  slug: string;
  description?: string;
  is_active: boolean;
  display_order: number;
  max_items_homepage: number;
  created_at: string;
  updated_at: string;
  categories?: Category[];
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  is_active: boolean;
  display_order: number;
  section_id: string;
  created_at: string;
  updated_at: string;
}

export interface SectionFormData {
  name: string;
  slug: string;
  description: string;
  icon: string;
  color: string;
  display_order: number;
  is_active: boolean;
  show_in_navigation: boolean;
  show_on_homepage: boolean;
  max_items_homepage: number;
  content_types: string[];
  filter_rules: Record<string, any>;
  category_ids: number[];
}

export interface CategoryFormData {
  name: string;
  slug: string;
  description?: string;
  is_active: boolean;
  section_id: string;
}

// Utility functions for sections
export const sectionUtils = {
  getDefaultFormData: (): SectionFormData => {
    return {
      name: '',
      slug: '',
      description: '',
      icon: 'Folder',
      color: '#e11d48',
      display_order: 1,
      is_active: true,
      show_in_navigation: true,
      show_on_homepage: true,
      max_items_homepage: 20,
      content_types: [],
      filter_rules: {},
      category_ids: []
    };
  },

  generateSlug: (name: string): string => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  },

  validateSectionForm: (data: Partial<SectionFormData>): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (!data.name?.trim()) {
      errors.push('Section name is required');
    }

    if (!data.slug?.trim()) {
      errors.push('Section slug is required');
    }

    if (!data.icon?.trim()) {
      errors.push('Section icon is required');
    }

    if (!data.color?.trim()) {
      errors.push('Section color is required');
    }

    if (data.max_items_homepage !== undefined && data.max_items_homepage < 1) {
      errors.push('Max items on homepage must be at least 1');
    }

    if (data.display_order !== undefined && data.display_order < 1) {
      errors.push('Display order must be at least 1');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  },

  validateSectionData: (data: Partial<SectionFormData>): string[] => {
    const validation = sectionUtils.validateSectionForm(data);
    return validation.errors;
  },

  validateCategoryData: (data: Partial<CategoryFormData>): string[] => {
    const errors: string[] = [];
    
    if (!data.name?.trim()) {
      errors.push('Category name is required');
    }
    
    if (!data.slug?.trim()) {
      errors.push('Category slug is required');
    }
    
    if (!data.section_id?.trim()) {
      errors.push('Section ID is required');
    }
    
    return errors;
  }
};
