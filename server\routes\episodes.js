/**
 * Episodes and Seasons Management API Routes
 * Handles CRUD operations for episodes and seasons of web series
 */
const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const db = require('../config/database');
const { authenticateToken, requireModerator } = require('../middleware/auth');

// Validation middleware for seasons
const seasonValidation = [
  body('seasonNumber').isInt({ min: 1 }).withMessage('Season number must be a positive integer'),
  body('title').optional().isLength({ max: 255 }).withMessage('Title must be less than 255 characters'),
  body('description').optional().isLength({ max: 1000 }).withMessage('Description must be less than 1000 characters'),
  body('posterUrl').optional().isURL().withMessage('Poster URL must be a valid URL'),
];

// Validation middleware for episodes
const episodeValidation = [
  body('episodeNumber').isInt({ min: 1 }).withMessage('Episode number must be a positive integer'),
  body('title').trim().isLength({ min: 1, max: 255 }).withMessage('Title is required and must be less than 255 characters'),
  body('description').optional().isLength({ max: 1000 }).withMessage('Description must be less than 1000 characters'),
  body('secureVideoLinks').optional().isLength({ max: 2000 }).withMessage('Video links must be less than 2000 characters'),
  body('runtime').optional().isLength({ max: 20 }).withMessage('Runtime must be less than 20 characters'),
  body('airDate').optional().isISO8601().withMessage('Air date must be a valid date'),
  body('thumbnailUrl').optional().isURL().withMessage('Thumbnail URL must be a valid URL'),
];

/**
 * Get all seasons for a content item
 * GET /api/episodes/content/:contentId/seasons
 */
router.get('/content/:contentId/seasons', async (req, res) => {
  try {
    const { contentId } = req.params;

    const [seasons] = await db.execute(`
      SELECT * FROM seasons 
      WHERE content_id = ? 
      ORDER BY season_number ASC
    `, [contentId]);

    // Get episodes for each season
    for (let season of seasons) {
      const [episodes] = await db.execute(`
        SELECT * FROM episodes 
        WHERE season_id = ? 
        ORDER BY episode_number ASC
      `, [season.id]);
      season.episodes = episodes;
    }

    res.json({
      success: true,
      data: seasons
    });

  } catch (error) {
    console.error('Error fetching seasons:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to fetch seasons'
    });
  }
});

/**
 * Create new season
 * POST /api/episodes/content/:contentId/seasons
 */
router.post('/content/:contentId/seasons', authenticateToken, requireModerator, seasonValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const { contentId } = req.params;
    const { seasonNumber, title, description, posterUrl } = req.body;

    // Check if season number already exists for this content
    const [existingSeason] = await db.execute(
      'SELECT id FROM seasons WHERE content_id = ? AND season_number = ?',
      [contentId, seasonNumber]
    );

    if (existingSeason.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: 'Season number already exists for this content'
      });
    }

    // Generate unique ID for season
    const seasonId = `season_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const [result] = await db.execute(`
      INSERT INTO seasons (
        id, content_id, season_number, title, description, poster_url, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [seasonId, contentId, seasonNumber, title || null, description || null, posterUrl || null]);

    // Update content total seasons count
    await db.execute(`
      UPDATE content SET 
        total_seasons = (SELECT COUNT(*) FROM seasons WHERE content_id = ?),
        updated_at = NOW()
      WHERE id = ?
    `, [contentId, contentId]);

    res.status(201).json({
      success: true,
      message: 'Season created successfully',
      data: {
        id: seasonId,
        contentId,
        seasonNumber,
        title,
        description,
        posterUrl,
        episodes: []
      }
    });

  } catch (error) {
    console.error('Error creating season:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to create season'
    });
  }
});

/**
 * Create new episode
 * POST /api/episodes/seasons/:seasonId/episodes
 */
router.post('/seasons/:seasonId/episodes', authenticateToken, requireModerator, episodeValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const { seasonId } = req.params;
    const { 
      episodeNumber, title, description, secureVideoLinks, 
      runtime, airDate, thumbnailUrl 
    } = req.body;

    // Get season info
    const [season] = await db.execute('SELECT content_id FROM seasons WHERE id = ?', [seasonId]);
    if (season.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'Season not found'
      });
    }

    const contentId = season[0].content_id;

    // Check if episode number already exists for this season
    const [existingEpisode] = await db.execute(
      'SELECT id FROM episodes WHERE season_id = ? AND episode_number = ?',
      [seasonId, episodeNumber]
    );

    if (existingEpisode.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: 'Episode number already exists for this season'
      });
    }

    // Generate unique ID for episode
    const episodeId = `episode_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    await db.execute(`
      INSERT INTO episodes (
        id, season_id, content_id, episode_number, title, description, 
        secure_video_links, runtime, air_date, thumbnail_url, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      episodeId, seasonId, contentId, episodeNumber, title, 
      description || null, secureVideoLinks || null, runtime || null, 
      airDate || null, thumbnailUrl || null
    ]);

    // Update content total episodes count
    await db.execute(`
      UPDATE content SET 
        total_episodes = (SELECT COUNT(*) FROM episodes WHERE content_id = ?),
        updated_at = NOW()
      WHERE id = ?
    `, [contentId, contentId]);

    res.status(201).json({
      success: true,
      message: 'Episode created successfully',
      data: {
        id: episodeId,
        seasonId,
        contentId,
        episodeNumber,
        title,
        description,
        secureVideoLinks,
        runtime,
        airDate,
        thumbnailUrl
      }
    });

  } catch (error) {
    console.error('Error creating episode:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to create episode'
    });
  }
});

/**
 * Update episode
 * PUT /api/episodes/:episodeId
 */
router.put('/:episodeId', authenticateToken, requireModerator, episodeValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const { episodeId } = req.params;
    const { 
      episodeNumber, title, description, secureVideoLinks, 
      runtime, airDate, thumbnailUrl 
    } = req.body;

    // Check if episode exists
    const [existingEpisode] = await db.execute('SELECT season_id, content_id FROM episodes WHERE id = ?', [episodeId]);
    if (existingEpisode.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'Episode not found'
      });
    }

    const { season_id: seasonId, content_id: contentId } = existingEpisode[0];

    // Check if episode number conflicts with other episodes in the same season
    const [conflictingEpisode] = await db.execute(
      'SELECT id FROM episodes WHERE season_id = ? AND episode_number = ? AND id != ?',
      [seasonId, episodeNumber, episodeId]
    );

    if (conflictingEpisode.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: 'Episode number already exists for this season'
      });
    }

    await db.execute(`
      UPDATE episodes SET 
        episode_number = ?, title = ?, description = ?, secure_video_links = ?,
        runtime = ?, air_date = ?, thumbnail_url = ?, updated_at = NOW()
      WHERE id = ?
    `, [
      episodeNumber, title, description || null, secureVideoLinks || null,
      runtime || null, airDate || null, thumbnailUrl || null, episodeId
    ]);

    // Update content updated_at
    await db.execute('UPDATE content SET updated_at = NOW() WHERE id = ?', [contentId]);

    res.json({
      success: true,
      message: 'Episode updated successfully'
    });

  } catch (error) {
    console.error('Error updating episode:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to update episode'
    });
  }
});

/**
 * Delete episode
 * DELETE /api/episodes/:episodeId
 */
router.delete('/:episodeId', authenticateToken, requireModerator, async (req, res) => {
  try {
    const { episodeId } = req.params;

    // Get episode info before deletion
    const [episode] = await db.execute('SELECT content_id FROM episodes WHERE id = ?', [episodeId]);
    if (episode.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'Episode not found'
      });
    }

    const contentId = episode[0].content_id;

    await db.execute('DELETE FROM episodes WHERE id = ?', [episodeId]);

    // Update content total episodes count
    await db.execute(`
      UPDATE content SET 
        total_episodes = (SELECT COUNT(*) FROM episodes WHERE content_id = ?),
        updated_at = NOW()
      WHERE id = ?
    `, [contentId, contentId]);

    res.json({
      success: true,
      message: 'Episode deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting episode:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to delete episode'
    });
  }
});

module.exports = router;
