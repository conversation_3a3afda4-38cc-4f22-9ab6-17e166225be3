/**
 * Simplified Authentication Context
 * Simple login/logout without complex session management
 */

import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';

// Simple types
interface AuthUser {
  id: string;
  username: string;
  role: string;
}

interface AuthState {
  isAuthenticated: boolean;
  user: AuthUser | null;
  isLoading: boolean;
  error: string | null;
}

interface AuthContextType {
  authState: AuthState;
  login: (credentials: { username: string; password: string }) => Promise<{ success: boolean; error?: string }>;
  logout: () => void;
  clearError: () => void;
  checkAuthStatus: () => boolean;
  hasPermission: (permission: string) => boolean;
  refreshSession: () => Promise<boolean>;
  isSessionValid: () => boolean;
  getTimeUntilExpiry: () => number;
}

// Simple actions
type AuthAction =
  | { type: 'LOGIN_START' }
  | { type: 'LOGIN_SUCCESS'; payload: AuthUser }
  | { type: 'LOGIN_FAILURE'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'CLEAR_ERROR' }
  | { type: 'RESTORE_SESSION'; payload: AuthUser };

const initialAuthState: AuthState = {
  isAuthenticated: false,
  user: null,
  isLoading: false,
  error: null,
};

// Simple reducer
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'LOGIN_START':
      return { ...state, isLoading: true, error: null };

    case 'LOGIN_SUCCESS':
    case 'RESTORE_SESSION':
      return {
        ...state,
        isAuthenticated: true,
        user: action.payload,
        isLoading: false,
        error: null,
      };

    case 'LOGIN_FAILURE':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        isLoading: false,
        error: action.payload,
      };

    case 'LOGOUT':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        isLoading: false,
        error: null,
      };

    case 'CLEAR_ERROR':
      return { ...state, error: null };

    default:
      return state;
  }
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Simple session storage
const SESSION_KEY = 'streamdb_auth_session';

function storeSession(user: AuthUser, token: string) {
  const sessionData = {
    user,
    token,
    timestamp: Date.now()
  };
  sessionStorage.setItem(SESSION_KEY, JSON.stringify(sessionData));
}

function getStoredSession() {
  try {
    const stored = sessionStorage.getItem(SESSION_KEY);
    if (stored) {
      return JSON.parse(stored);
    }
  } catch (error) {
    console.error('Failed to parse stored session:', error);
  }
  return null;
}

function clearSession() {
  sessionStorage.removeItem(SESSION_KEY);
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [authState, dispatch] = useReducer(authReducer, initialAuthState);

  // Restore session on app load
  useEffect(() => {
    const sessionData = getStoredSession();
    if (sessionData && sessionData.user) {
      dispatch({ type: 'RESTORE_SESSION', payload: sessionData.user });
    }
  }, []);

  // Simple login function
  const login = useCallback(async (credentials: { username: string; password: string }) => {
    dispatch({ type: 'LOGIN_START' });

    try {
      // Call API service for authentication
      const apiService = await import('@/services/apiService');
      const response = await apiService.default.login(credentials);

      if (response?.success && response?.user) {
        const user: AuthUser = {
          id: response.user.id || 'admin',
          username: response.user.username || credentials.username,
          role: response.user.role || 'admin',
        };

        // Store session simply
        storeSession(user, response.token || 'simple-token');

        dispatch({ type: 'LOGIN_SUCCESS', payload: user });
        return { success: true };
      } else {
        const error = response?.error || 'Authentication failed';
        dispatch({ type: 'LOGIN_FAILURE', payload: error });
        return { success: false, error };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      dispatch({ type: 'LOGIN_FAILURE', payload: errorMessage });
      return { success: false, error: errorMessage };
    }
  }, []);

  // Simple logout function
  const logout = useCallback(() => {
    clearSession();
    dispatch({ type: 'LOGOUT' });
  }, []);

  // Clear error function
  const clearError = useCallback(() => {
    dispatch({ type: 'CLEAR_ERROR' });
  }, []);

  // Simple implementations of required functions
  const checkAuthStatus = useCallback(() => {
    return authState.isAuthenticated;
  }, [authState.isAuthenticated]);

  const hasPermission = useCallback((permission: string) => {
    // Simple permission check - admin has all permissions
    return authState.isAuthenticated && authState.user?.role === 'admin';
  }, [authState.isAuthenticated, authState.user?.role]);

  const refreshSession = useCallback(async () => {
    // Simple refresh - just check if session exists
    const sessionData = getStoredSession();
    return !!sessionData;
  }, []);

  const isSessionValid = useCallback(() => {
    return authState.isAuthenticated;
  }, [authState.isAuthenticated]);

  const getTimeUntilExpiry = useCallback(() => {
    // Return a large number since we don't have session expiry
    return 24 * 60 * 60 * 1000; // 24 hours
  }, []);

  const contextValue: AuthContextType = {
    authState,
    login,
    logout,
    clearError,
    checkAuthStatus,
    hasPermission,
    refreshSession,
    isSessionValid,
    getTimeUntilExpiry,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
